'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import OptimizedImage from './OptimizedImage'
import { BlogPost } from '@/types'

interface BlogGridProps {
  posts: BlogPost[]
}

// Pastel colors for read time circles
const pastelColors = [
  'bg-pink-100 text-pink-700 dark:bg-pink-900/30 dark:text-pink-300',
  'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300',
  'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300',
  'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300',
  'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300',
  'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300',
]

export default function BlogGrid({ posts }: BlogGridProps) {
  if (posts.length === 0) {
    return (
      <div className="text-center py-16">
        <h2 className="text-2xl font-semibold text-primary-400 mb-4">No blog posts yet</h2>
        <p className="text-primary-300">Check back soon for new content!</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 auto-rows-fr">
      {posts.map((post, index) => (
        <motion.article
          key={post.slug}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="group h-full"
        >
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden h-full flex flex-col">
            {/* Featured Image - Taller, no borders */}
            <div className="relative h-72 overflow-hidden flex-shrink-0">
              <OptimizedImage
                src={post.featuredImage}
                alt={`Featured image for ${post.title}`}
                width={400}
                height={288}
                className="object-cover object-center group-hover:scale-105 transition-transform duration-300 w-full h-full"
                priority={index < 3} // Prioritize first 3 images for LCP
                placeholder="blur"
                blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
              />
            </div>

            {/* Content */}
            <div className="p-6 flex flex-col flex-grow">
              {/* Categories */}
              {post.categories && post.categories.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-3">
                  {post.categories.slice(0, 2).map((category) => (
                    <span
                      key={category}
                      className="px-3 py-1 bg-primary-50 dark:bg-gray-700 text-primary-600 dark:text-gray-100 text-xs rounded-full font-medium"
                    >
                      {category}
                    </span>
                  ))}
                </div>
              )}

              {/* Title - Clickable */}
              <Link href={`/blog/${post.slug}`} className="no-link-style">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300 line-clamp-2 cursor-pointer">
                  {post.title}
                </h2>
              </Link>

              {/* Excerpt - Exactly 3 lines */}
              <p className="text-gray-600 dark:text-gray-300 line-clamp-3 mb-4 flex-grow leading-relaxed">
                {post.excerpt}
              </p>

              {/* Bottom section with read time, tags, and date */}
              <div className="mt-auto space-y-3">
                {/* Read Time Circle and Tags */}
                <div className="flex items-center justify-between">
                  {/* Circular Read Time - Bigger with padding */}
                  <div className={`w-16 h-16 rounded-full flex flex-col items-center justify-center text-xs font-medium ${pastelColors[index % pastelColors.length]}`}>
                    <span className="font-bold text-sm">{post.readTime}</span>
                    <span className="text-[10px] leading-tight">min</span>
                    <span className="text-[10px] leading-tight">read</span>
                  </div>

                  {/* Tags - Smaller font */}
                  {post.tags && post.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 flex-1 ml-3">
                      {post.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-[10px] rounded-md font-normal"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                {/* Date */}
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <time dateTime={post.date}>
                    {new Date(post.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </time>
                </div>
              </div>
            </div>
          </div>
        </motion.article>
      ))}
    </div>
  )
}
